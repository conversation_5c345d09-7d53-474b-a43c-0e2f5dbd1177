<template>
  <div class="nested-table-test">
    <h2>嵌套表格功能测试</h2>
    
    <div class="control-panel">
      <button @click="loadNestedTableData" class="btn-primary">加载嵌套表格示例</button>
      <button @click="clearData" class="btn-secondary">清空数据</button>
      <button @click="exportData" class="btn-info">导出数据</button>
    </div>

    <div class="info-panel">
      <h3>操作说明</h3>
      <ul>
        <li>点击"加载嵌套表格示例"按钮加载预设的嵌套表格数据</li>
        <li>右键点击数据单元格，选择"添加嵌套表格"来手动创建嵌套表格</li>
        <li>在嵌套表格中可以直接编辑单元格内容</li>
        <li>右键点击包含嵌套表格的单元格，选择"移除嵌套表格"来删除</li>
      </ul>
    </div>

    <div class="table-section">
      <TableContainer
        ref="tableContainer"
        :table-width="'100%'"
        :table-height="'600px'"
        :data-rows="tableData"
        :enable-nested-tables="true"
        :nested-level="0"
        :max-nested-level="2"
        :use-dynamic-header="true"
        @nested-table-created="handleNestedTableCreated"
        @nested-table-removed="handleNestedTableRemoved"
        @nested-cell-start-edit="handleNestedCellStartEdit"
        @nested-cell-finish-edit="handleNestedCellFinishEdit"
      />
    </div>

    <div class="debug-panel">
      <h3>调试信息</h3>
      <div class="debug-content">
        <pre>{{ debugInfo }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import { getPresetData } from '@/data/table-presets.js'

export default {
  name: 'NestedTableTest',
  components: {
    TableContainer
  },
  data() {
    return {
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],
      debugInfo: '等待操作...'
    }
  },
  methods: {
    // 加载嵌套表格数据
    loadNestedTableData() {
      console.log('开始加载嵌套表格数据...')
      
      const presetData = getPresetData('nestedTable')
      console.log('获取到的预设数据:', presetData)
      
      if (presetData) {
        const tableContainer = this.$refs.tableContainer
        
        // 设置表头配置
        if (presetData.headers && presetData.headerMerges) {
          const headerConfig = {
            headers: presetData.headers,
            merges: presetData.headerMerges
          }
          
          tableContainer.setDynamicHeaderConfig(
            true,
            headerConfig,
            presetData.headerWidthConfig,
            presetData.verticalHeadersConfig
          )
        }
        
        // 插入数据
        const result = tableContainer.insertDataFromJSON(presetData, {
          clearExisting: true,
          validateData: true
        })
        
        console.log('数据插入结果:', result)
        this.debugInfo = `数据加载结果: ${result.success ? '成功' : '失败'}\n消息: ${result.message}`
        
        if (result.success) {
          this.tableData = tableContainer.dataRows
          console.log('更新后的表格数据:', this.tableData)
        }
      } else {
        this.debugInfo = '错误: 未找到嵌套表格预设数据'
      }
    },

    // 清空数据
    clearData() {
      this.tableData = [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ]
      this.debugInfo = '数据已清空'
    },

    // 导出数据
    exportData() {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        const data = tableContainer.getDataAsJSON({
          includeEmpty: false,
          includeMergeInfo: true,
          includeNestedTables: true
        })
        
        console.log('导出的数据:', data)
        this.debugInfo = `导出数据成功，包含 ${data.cellRows.length} 行数据`
        
        // 下载JSON文件
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'nested-table-test-data.json'
        a.click()
        URL.revokeObjectURL(url)
      }
    },

    // 处理嵌套表格创建
    handleNestedTableCreated(event) {
      console.log('嵌套表格创建事件:', event)
      this.debugInfo = `嵌套表格创建成功 - 位置: (${event.rowIndex}, ${event.cellIndex}), 大小: ${event.config.config.columns}x${event.config.config.rows}`
    },

    // 处理嵌套表格移除
    handleNestedTableRemoved(event) {
      console.log('嵌套表格移除事件:', event)
      this.debugInfo = `嵌套表格移除成功 - 位置: (${event.rowIndex}, ${event.cellIndex})`
    },

    // 处理嵌套单元格开始编辑
    handleNestedCellStartEdit(event) {
      console.log('嵌套单元格开始编辑:', event)
      this.debugInfo = `开始编辑嵌套单元格 - 父位置: (${event.parentRowIndex}, ${event.parentCellIndex}), 嵌套位置: (${event.nestedRowIndex}, ${event.nestedCellIndex})`
    },

    // 处理嵌套单元格完成编辑
    handleNestedCellFinishEdit(event) {
      console.log('嵌套单元格完成编辑:', event)
      this.debugInfo = `嵌套单元格编辑完成 - 新内容: "${event.newContent}"`
    }
  }
}
</script>

<style scoped>
.nested-table-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.control-panel {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.btn-primary {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-info {
  padding: 10px 20px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-info:hover {
  background: #138496;
}

.info-panel {
  margin-bottom: 20px;
  padding: 15px;
  background: #e9ecef;
  border-radius: 8px;
}

.info-panel h3 {
  margin-top: 0;
  color: #333;
}

.info-panel ul {
  padding-left: 20px;
}

.info-panel li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.table-section {
  margin-bottom: 20px;
}

.debug-panel {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.debug-panel h3 {
  margin-top: 0;
  color: #333;
}

.debug-content {
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  max-height: 200px;
  overflow-y: auto;
}

.debug-content pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
