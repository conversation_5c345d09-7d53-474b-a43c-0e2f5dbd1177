<template>
  <div class="simple-nested-test">
    <h2>简单嵌套表格测试</h2>
    
    <div class="control-panel">
      <button @click="createSimpleNestedTable" class="btn-primary">创建简单嵌套表格</button>
      <button @click="checkTableData" class="btn-info">检查表格数据</button>
    </div>

    <div class="table-section">
      <TableContainer
        ref="tableContainer"
        :table-width="'100%'"
        :table-height="'400px'"
        :data-rows="tableData"
        :enable-nested-tables="true"
        :nested-level="0"
        :max-nested-level="2"
      />
    </div>

    <div class="debug-panel">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'

export default {
  name: 'SimpleNestedTest',
  components: {
    TableContainer
  },
  data() {
    return {
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],
      debugInfo: '等待操作...'
    }
  },
  methods: {
    // 创建简单的嵌套表格
    createSimpleNestedTable() {
      console.log('开始创建简单嵌套表格...')
      
      // 确保有足够的行
      while (this.tableData.length < 2) {
        this.tableData.push(
          Array(8).fill(null).map(() => ({
            content: '',
            isEditing: false,
            originalContent: '',
            hasMath: false
          }))
        )
      }
      
      // 在第一行第三列创建嵌套表格
      const cell = this.tableData[0][2]
      
      // 设置主单元格内容
      this.$set(cell, 'content', '嵌套表格测试')
      
      // 创建嵌套表格配置
      const nestedTableConfig = {
        enabled: true,
        config: {
          columnWidths: [100, 100],
          cellRows: [
            [
              {content: "项目1", originContent: "项目1", hasMath: false},
              {content: "值1", originContent: "值1", hasMath: false}
            ],
            [
              {content: "项目2", originContent: "项目2", hasMath: false},
              {content: "值2", originContent: "值2", hasMath: false}
            ]
          ],
          metadata: {
            title: "测试嵌套表格",
            level: 1,
            parentCell: {row: 0, col: 2},
            columns: 2,
            rows: 2
          }
        }
      }
      
      // 设置嵌套表格
      this.$set(cell, 'nestedTable', nestedTableConfig)
      
      console.log('嵌套表格配置已设置:', nestedTableConfig)
      console.log('单元格数据:', cell)
      
      this.debugInfo = `嵌套表格已创建\n位置: (0, 2)\n配置: ${JSON.stringify(nestedTableConfig, null, 2)}`
      
      // 强制更新
      this.$forceUpdate()
    },

    // 检查表格数据
    checkTableData() {
      console.log('当前表格数据:', this.tableData)
      
      let hasNestedTable = false
      let nestedTableInfo = []
      
      this.tableData.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (cell.nestedTable && cell.nestedTable.enabled) {
            hasNestedTable = true
            nestedTableInfo.push({
              position: `(${rowIndex}, ${cellIndex})`,
              content: cell.content,
              config: cell.nestedTable.config
            })
          }
        })
      })
      
      this.debugInfo = `表格数据检查结果:\n` +
        `总行数: ${this.tableData.length}\n` +
        `总列数: ${this.tableData[0] ? this.tableData[0].length : 0}\n` +
        `包含嵌套表格: ${hasNestedTable ? '是' : '否'}\n` +
        `嵌套表格信息: ${JSON.stringify(nestedTableInfo, null, 2)}`
      
      console.log('表格数据检查完成:', {
        hasNestedTable,
        nestedTableInfo
      })
    }
  }
}
</script>

<style scoped>
.simple-nested-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.control-panel {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.btn-primary {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-info {
  padding: 10px 20px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-info:hover {
  background: #138496;
}

.table-section {
  margin-bottom: 20px;
}

.debug-panel {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.debug-panel h3 {
  margin-top: 0;
  color: #333;
}

.debug-panel pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
